'use client'

import React, { 
  useCallback,
  useEffect, useRef, useState,
} from 'react';
import * as fabric from 'fabric';
import {
  Wand2,
  Upload,
  Type,
  Clock,
  Image as ImageIcon,
  LucideProps,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Sparkles,
} from 'lucide-react';
import autosize from 'autosize';
import {
  acceptedImageMimeTypes, FILE_SIZE_10_MB, ImageStyleGroups,
} from '@/common/constants';
import { getPath } from '@/common/utils/helpers';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import {
  projectImageStorage, ImageMetadata,
} from '@/common/utils/projectImageStorage';
import {
  Button, TextArea, CircularSpinner,
} from '../../atoms';
import { Dropdown } from '../../molecules';

interface CanvasSidebarProps {
  canvas: fabric.Canvas | null;
  agentId: string;
  planId: string;
}

interface SidebarTab {
  id: string;
  icon: React.ForwardRefExoticComponent<Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>>;
  label: string;
  content: React.ComponentType<{
    canvas: fabric.Canvas | null;
    agentId?: string;
    planId?: string;
  }>;
}

const scaleImageToFitCanvas = (img: fabric.FabricImage, canvas: fabric.Canvas) => {
  const canvasWidth = canvas.width!;
  const canvasHeight = canvas.height!;

  // Set the image to exactly match canvas dimensions and fill completely
  img.set({
    left: 0,
    top: 0,
    scaleX: canvasWidth / img.width!,
    scaleY: canvasHeight / img.height!,
    originX: 'left',
    originY: 'top',
    selectable: true,
    evented: true,
  });

  // Ensure the image coordinates are updated
  img.setCoords();

  canvas.add(img);
};

const GenerateImagePanel = ({
  canvas,
  agentId,
  planId,
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof ImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof ImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const requestBody: any = {
        description: imagePrompt,
        planId: planId || 'new-post',
        seed: seed,
        guidanceScale: guidanceScale,
      };

      if (selectedStyle && selectedStyle.option !== 'none') {
        requestBody.style = [selectedStyle.option];
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        toast.error('Failed to generate image');
        setError(`${errorData.error.includes("NSFW") ? `${errorData.error} ` : ""}` || 'Failed to generate image');
        setIsGenerating(false);
        return
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const imageUrl = getPath(imageData.filepath);

        fabric.FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' }).then((img: fabric.FabricImage) => {
          scaleImageToFitCanvas(img, canvas);
          canvas.renderAll();
        });

        if (activeProject?.project_id && agentId) {
          const fileName = `Generated Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          await projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            imageUrl,
            fileName,
            planId,
            imagePrompt,
          );
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }

        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Image generated and added to canvas!');
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
      setError('Failed to generate image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Image Style</h3>
            <p className="text-gray-400 text-sm">Create and add images using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(ImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-xl border transition-all duration-200 text-left group aspect-square border-neutral-600 hover:border-neutral-500 bg-neutral-800"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              {style.option === "none" ? (
                                <div className="flex items-center justify-center h-full rounded-xl bg-gradient-to-br from-neutral-800 to-neutral-700 text-white text-xs font-medium">
                                  Create Your Own Style
                                </div>
                              ) : (
                                <Image
                                  src={`/images/style-samples/${style.option}.png`}
                                  alt={style.label}
                                  width={200}
                                  height={200}
                                  quality={30}
                                  className='rounded-xl h-full w-full object-cover'
                                />
                              )}
                            </div>
                            <div className="text-[10px] absolute bottom-2 right-2 font-medium text-neutral-900 bg-white/90 rounded-lg py-1 px-2 backdrop-blur-sm truncate shadow-sm max-w-[calc(100%-16px)]">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Generate Image</h3>
                <p className="text-gray-400 text-sm">
                  Style: {selectedStyle?.label || 'None'}
                </p>
              </div>
            </div>
          </div>
          <div className='flex flex-col space-y-4'>
            <label htmlFor="image-prompt" className="text-white font-medium text-sm">
              Describe your image
              <TextArea
                ref={imagePromptRef}
                name="image-prompt"
                id="image-prompt"
                width='w-full'
                maxHeight='80px'
                disabled={isGenerating}
                placeholder='Describe the image you want to generate...'
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
              />
            </label>
            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
                
              </div>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Guidance Scale: {guidanceScale}
              </label>
              <input
                type="range"
                min="0"
                max="20"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              onClick={handleGenerate}
              disabled={isGenerating || !imagePrompt.trim()}
              variant='gradient'
              size='md'
              width='w-full'
            >
              {isGenerating ? 'Generating...' : 'Generate Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
          <div className="text-xs mt-4 text-gray-500 bg-neutral-800 p-3 rounded-xl">
            <p className="mb-1">💡 <strong>Tips:</strong></p>
            <p>• Higher guidance scale = more adherence to your prompt</p>
            <p>• Different seeds produce different variations</p>
          </div>
        </>
      )}
    </div>
  );
};

const ImproveImagePanel = ({
  canvas,
  agentId,
  planId,
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<typeof ImageStyleGroups.none.styles[0] | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');
  const [hasCanvasContent, setHasCanvasContent] = useState(false);
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: typeof ImageStyleGroups.none.styles[0]) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  useEffect(() => {
    const checkCanvasContent = () => {
      if (canvas) {
        const objects = canvas.getObjects();
        setHasCanvasContent(objects.length > 0);
      } else {
        setHasCanvasContent(false);
      }
    };

    checkCanvasContent();

    if (canvas) {
      canvas.on('object:added', checkCanvasContent);
      canvas.on('object:removed', checkCanvasContent);
      canvas.on('canvas:cleared', checkCanvasContent);

      return () => {
        canvas.off('object:added', checkCanvasContent);
        canvas.off('object:removed', checkCanvasContent);
        canvas.off('canvas:cleared', checkCanvasContent);
      };
    }
  }, [canvas]);

  const uploadCanvasImage = async (): Promise<string | null> => {
    if (!canvas) {
      return null;
    }

    try {
      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();

      const formData = new FormData();
      const timestamp = Date.now();
      const fileName = `canvas-improve-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-improve');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload canvas image: ${uploadResponse.statusText}`);
      }

      const uploadData = await uploadResponse.json();
      return uploadData.filepath || null;
    } catch (error) {
      console.error('Error uploading canvas image:', error);
      return null;
    }
  };

  const handleImprove = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter a description for improvement');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    if (!hasCanvasContent) {
      setError('Canvas must have content to improve');
      return;
    }

    setError('');
    setIsGenerating(true);

    try {
      const imageUrl = await uploadCanvasImage();

      if (!imageUrl) {
        throw new Error('Failed to upload canvas image');
      }

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const requestBody: any = {
        description: imagePrompt,
        planId: planId || 'new-post',
        imageUrl,
        seed: seed,
        guidanceScale: guidanceScale,
        modelNameFlux: 'black-forest-labs/FLUX.1-kontext-pro',
      };

      if (selectedStyle && selectedStyle.option !== 'none') {
        requestBody.style = [selectedStyle.option];
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to improve image: ${response.statusText}`);
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath && canvas) {
        const improvedImageUrl = getPath(imageData.filepath);

        fabric.FabricImage.fromURL(improvedImageUrl, { crossOrigin: 'anonymous' }).then((img: fabric.FabricImage) => {
          canvas.clear();
          scaleImageToFitCanvas(img, canvas);
          canvas.renderAll();
        });

        if (activeProject?.project_id && agentId) {
          const fileName = `Improved Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
          await projectImageStorage.addGeneratedImage(
            activeProject.project_id,
            agentId,
            improvedImageUrl,
            fileName,
            planId,
            imagePrompt,
          );
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }

        trackContentEvent('image', {
          prompt: imagePrompt,
          enhanceType: 'improve',
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Canvas has been updated!');
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error improving image:', error);
      toast.error('Failed to improve image');
      setError('Failed to improve image. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="px-6 py-4">
      {!hasCanvasContent ? (
        <>
          <div className="mb-6">
            <h3 className="text-white font-semibold text-lg">Improve Image</h3>
            <p className="text-gray-400 text-sm">Enhance your canvas design with AI</p>
          </div>
          <div className="text-center py-12">
            <Sparkles className="mx-auto mb-3 text-gray-500" size={32} />
            <p className="text-gray-400 text-sm">Add content to canvas first</p>
            <p className="text-gray-500 text-xs mt-1">Create or upload images to enable improvements</p>
          </div>
        </>
      ) : currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Improvement Style</h3>
            <p className="text-gray-400 text-sm">Select a style for your canvas improvement</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className=" pr-2">
                {Object.entries(ImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-xl border transition-all duration-200 text-left group aspect-square border-neutral-600 hover:border-neutral-500 bg-neutral-800"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              {style.option === "none" ? (
                                <div className="flex items-center justify-center h-full rounded-xl bg-gradient-to-br from-neutral-800 to-neutral-700 text-white text-xs font-medium">
                                  Create Your Own Style
                                </div>
                              ) : (
                                <Image
                                  src={`/images/style-samples/${style.option}.png`}
                                  alt={style.label}
                                  width={200}
                                  height={200}
                                  quality={30}
                                  className='rounded-xl h-full w-full object-cover'
                                />
                              )}
                            </div>
                            <div className="text-[10px] absolute bottom-2 right-2 font-medium text-neutral-900 bg-white/90 rounded-lg py-1 px-2 backdrop-blur-sm truncate shadow-sm max-w-[calc(100%-16px)]">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Improve Image</h3>
                <p className="text-gray-400 text-sm">
                  Style: {selectedStyle?.label || 'None'}
                </p>
              </div>
            </div>
          </div>
          <div className='flex flex-col space-y-4'>
            <label htmlFor="improve-prompt" className="text-white font-medium text-sm">
              Improvement Description
              <TextArea
                ref={imagePromptRef}
                name="improve-prompt"
                id="improve-prompt"
                width='w-full'
                maxHeight='80px'
                disabled={isGenerating}
                placeholder='Describe how you want to improve the current design...'
                value={imagePrompt}
                onChange={(e) => setImagePrompt(e.target.value)}
              />
            </label>
            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
              </div>
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Guidance Scale: {guidanceScale}
              </label>
              <input
                type="range"
                min="0"
                max="20"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              onClick={handleImprove}
              disabled={isGenerating || !imagePrompt.trim() || !hasCanvasContent}
              variant='gradient'
              size='md'
              width='w-full'
            >
              {isGenerating ? 'Improving...' : 'Improve Design'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>

          <div className="text-xs mt-4 text-gray-500 bg-neutral-800 p-3 rounded-xl">
            <p className="mb-1">💡 <strong>Tips:</strong></p>
            <p>• Describe specific improvements you want</p>
            <p>• The AI will use your current canvas as reference</p>
            <p>• Higher guidance scale = more adherence to your prompt</p>
            <p>• Different seeds produce different variations</p>
            <p>• Selected style will influence the improvement direction</p>
          </div>
        </>
      )}
    </div>
  );
};

const UploadImagePanel = ({
  canvas, agentId, planId,
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const { activeProject } = useProjectContext();

  const validateFile = (file: File): string | null => {

    if (!acceptedImageMimeTypes.includes(file.type)) {
      return 'Please upload a valid image file (JPEG, PNG, GIF, SVG, or WebP)';
    }

    if (file.size > FILE_SIZE_10_MB) {
      return 'File size must be less than 10MB';
    }

    return null;
  };

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      return;
    }

    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imgUrl = event.target?.result as string;
        if (canvas) {
          fabric.FabricImage.fromURL(imgUrl).then((img: fabric.FabricImage) => {
            scaleImageToFitCanvas(img, canvas);
            canvas.renderAll();

            if (activeProject?.project_id && agentId) {
              projectImageStorage.addUploadedImage(
                activeProject.project_id,
                agentId,
                imgUrl,
                file.name,
                planId,
              ).then(() => {
                window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
              }).catch((error) => {
                console.error('Error storing uploaded image:', error);
              });
            }

            toast.success('Image added to canvas!');
          });
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Upload Image</h3>
        <p className="text-gray-400 text-sm">Add images from your device</p>
      </div>
      <div className="space-y-4">
        <div
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          className={`border-2 border-dashed rounded-xl p-8 text-center transition-all cursor-pointer ${
            isDragging
              ? 'border-violets-are-blue bg-violets-are-blue/10'
              : 'border-neutral-600 hover:border-violets-are-blue'
          } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ImageIcon className="mx-auto mb-3 text-gray-400" size={40} />
          <p className="text-gray-400 text-sm mb-2">
            {isDragging ? 'Drop image here' : 'Drag & drop or click to upload'}
          </p>
          <p className="text-gray-500 text-xs">PNG, JPG, SVG, GIF, WebP up to 10MB</p>
          {isUploading && (
            <div className="mt-3">
              <div className="text-violets-are-blue text-sm">Uploading...</div>
            </div>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleFileInputChange}
          disabled={isUploading}
        />

        <div className="text-xs text-gray-500">
          <p>• Supported formats: JPEG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB</p>
        </div>
      </div>
    </div>
  );
};

const TextPanel = ({ canvas }: { canvas: fabric.Canvas | null; agentId?: string; planId?: string; }) => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#000000');
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [textAlign, setTextAlign] = useState('center');

  const fonts = [
    'Arial',
    'Brush Script MT',
    'Chillax',
    'Courier New',
    'Georgia',
    'Impact',
    'Tahoma',
    'Trebuchet MS',
    'Verdana',
  ];

  const addText = () => {
    if (!canvas) {
      return;
    }
    const text = new fabric.IText('Double-click to edit', {
      left: 100,
      top: 100,
      fontFamily: fontFamily,
      fontSize: fontSize,
      fill: textColor,
      fontWeight: isBold ? 'bold' : 'normal',
      fontStyle: isItalic ? 'italic' : 'normal',
      textAlign: textAlign as 'left' | 'center' | 'right',
      editable: true,
    });

    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
    toast.success('Text added to canvas!');
  };

  const updateSelectedText = useCallback(() => {
    if (!canvas) {
      return;
    }
    const activeObject = canvas.getActiveObject();
    if (activeObject && (activeObject.type === 'text' || activeObject.type === 'i-text')) {
      const textObject = activeObject as fabric.IText;
      textObject.set({
        fontFamily: fontFamily,
        fontSize: fontSize,
        fill: textColor,
        fontWeight: isBold ? 'bold' : 'normal',
        fontStyle: isItalic ? 'italic' : 'normal',
        textAlign: textAlign as 'left' | 'center' | 'right',
      });
      canvas.renderAll();
    }
  }, [canvas, fontFamily, fontSize, textColor, isBold, isItalic, textAlign]);

  React.useEffect(() => {
    updateSelectedText();
  }, [fontFamily, fontSize, textColor, isBold, isItalic, textAlign, updateSelectedText]);

  return (
    <div className="px-6 py-4">
      <div className="mb-6">
        <h3 className="text-white font-semibold text-lg">Text Tools</h3>
        <p className="text-gray-400 text-sm">Add and customize text</p>
      </div>
      <div className="space-y-6">
       

        <div className="space-y-4">
          <div>
            <Dropdown
              label="Font Family"
              options={fonts.map(font => ({ 
                value: font, 
                label: font,
              }))}
              selectedOption={{ 
                label: fontFamily, 
                option: fontFamily,
              }}
              onSelect={(option) => {
                setFontFamily(option.label)
              }}
              placeholder="Select Font Style"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Font Size: {fontSize}px
            </label>
            <input
              type="range"
              min="12"
              max="120"
              value={fontSize}
              onChange={(e) => setFontSize(Number(e.target.value))}
              className="w-full accent-violets-are-blue"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Color</label>
            <input
              type="color"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded-lg p-0.5 py-0 border border-neutral-600"
            />
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Style</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsBold(!isBold)}
                className={`${isBold ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Bold
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setIsItalic(!isItalic)}
                className={`${isItalic ? "text-violets-are-blue !border-violets-are-blue" : ""}`}
              >
                Italic
              </Button>
            </div>
          </div>

          <div>
            <label className="text-white text-sm font-medium mb-2 block">Text Alignment</label>
            <div className="flex gap-2">
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('left')}
                className={`${textAlign === 'left' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Left"
              >
                <AlignLeft size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('center')}
                className={`${textAlign === 'center' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Center"
              >
                <AlignCenter size={16} />
              </Button>
              <Button
                variant='outline-rounded'
                size='sm'
                onClick={() => setTextAlign('right')}
                className={`${textAlign === 'right' ? "text-violets-are-blue !border-violets-are-blue" : "!text-gray-500 !border-gray-500 hover:!text-white"} !px-3`}
                title="Align Right"
              >
                <AlignRight size={16} />
              </Button>
            </div>
          </div>
        </div>
        <Button
          variant="gradient"
          size="md"
          width="w-full"
          onClick={addText}
        >
          Add Text
        </Button>
        <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-xl mt-auto">
          <p className="mb-1">💡 <strong>Tips:</strong></p>
          <p>• Select text on canvas to modify style & alignment</p>
          <p>• Double-click text to edit content</p>
          <p>• Use Delete key to remove selected text</p>
        </div>
      </div>
    </div>
  );
};



const RecentUploadsPanel = ({
  canvas,
}: {
  canvas: fabric.Canvas | null;
  agentId?: string;
  planId?: string;
}) => {
  const [generatedImages, setGeneratedImages] = useState<ImageMetadata[]>([]);
  const [uploadedImages, setUploadedImages] = useState<ImageMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingImages, setIsFetchingImages] = useState(false);
  const { activeProject } = useProjectContext();

  const fetchRecentImages = React.useCallback(async () => {
    if (!activeProject?.project_id) {
      setGeneratedImages([]);
      setUploadedImages([]);
      return;
    }

    setIsFetchingImages(true);
    try {

      const projectImages = await projectImageStorage.getRecentImages(activeProject.project_id);
      setGeneratedImages(projectImages.generated);
      setUploadedImages(projectImages.uploaded);
    } catch (error) {
      console.error('Error fetching recent images:', error);
    } finally {
      setIsFetchingImages(false);
    }
  }, [activeProject?.project_id]);

  React.useEffect(() => {
    fetchRecentImages();
  }, [fetchRecentImages]);

  React.useEffect(() => {
    const handleProjectImagesUpdate = (event: CustomEvent) => {
      if (event.detail?.projectId === activeProject?.project_id) {
        fetchRecentImages();
      }
    };

    window.addEventListener('projectImagesUpdated', handleProjectImagesUpdate as EventListener);

    return () => {
      window.removeEventListener('projectImagesUpdated', handleProjectImagesUpdate as EventListener);
    };
  }, [activeProject?.project_id, fetchRecentImages]);

  const addImageToCanvas = (imageUrl: string, imageName: string) => {
    if (!canvas) {
      return;
    }
    setIsLoading(true);
    fabric.FabricImage.fromURL(imageUrl, { crossOrigin: 'anonymous' }).then((img: fabric.FabricImage) => {
      scaleImageToFitCanvas(img, canvas);
      canvas.setActiveObject(img);
      canvas.renderAll();
      toast.success(`${imageName} added to canvas!`);
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
      toast.error('Failed to load image');
    });
  };

  const addImageFromMetadata = (imageData: ImageMetadata) => {
    addImageToCanvas(imageData.filePath, imageData.fileName || 'Image');
  };

  const clearRecentImages = async () => {
    if (!activeProject?.project_id) {
      return;
    }

    setIsFetchingImages(true);
    try {
      await projectImageStorage.clearProjectImages(activeProject.project_id);
      setGeneratedImages([]);
      setUploadedImages([]);
      toast.success('Recent images cleared!');
    } catch (error) {
      console.error('Error clearing recent images:', error);
      toast.error('Failed to clear recent images');
    } finally {
      setIsFetchingImages(false);
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const totalImages = generatedImages.length + uploadedImages.length;

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-white font-semibold text-lg">Recent Images</h3>
          {totalImages > 0 && (
            <button
              onClick={clearRecentImages}
              className="text-xs text-gray-400 hover:text-red-400 transition-colors"
            >
              Clear all
            </button>
          )}
        </div>
        <p className="text-gray-400 text-sm">Generated and uploaded images for this project</p>
      </div>

      {!activeProject ? (
        <div className="text-center py-12">
          <div className="text-violets-are-blue text-sm">Loading project...</div>
        </div>
      ) : isFetchingImages ? (
        <div className="text-center py-12">
          <div className="flex flex-col items-center gap-3">
            <CircularSpinner />
            <div className="text-white text-sm">Loading recent images...</div>
          </div>
        </div>
      ) : totalImages === 0 ? (
        <div className="text-gray-400 text-sm text-center py-12">
          <ImageIcon className="mx-auto mb-3 text-gray-500" size={32} />
          <p>No recent images</p>
          <p className="text-xs mt-1">Generate or upload images to see them here</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Generated Images Section */}
          {generatedImages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-semibold mb-3 border-b border-neutral-700 pb-2">
                Generated Images ({generatedImages.length})
              </h4>
              <div className="space-y-3">
                {generatedImages.map((image) => (
                  <div
                    key={image.id}
                    className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
                    onClick={() => addImageFromMetadata(image)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={image.filePath}
                          alt={image.fileName || 'Generated image'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                          {image.description || image.fileName || 'Generated Image'}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatDate(new Date(image.createdAt).getTime())}
                        </p>
                      </div>
                      <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Uploaded Images Section */}
          {uploadedImages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-semibold mb-3 border-b border-neutral-700 pb-2">
                Uploaded Images ({uploadedImages.length})
              </h4>
              <div className="space-y-3">
                {uploadedImages.map((image) => (
                  <div
                    key={image.id}
                    className="bg-neutral-800 rounded-lg p-3 border border-neutral-700 hover:border-violets-are-blue transition-colors cursor-pointer group"
                    onClick={() => addImageFromMetadata(image)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={image.filePath}
                          alt={image.fileName || 'Uploaded image'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white text-sm font-medium truncate group-hover:text-violets-are-blue transition-colors">
                          {image.fileName || 'Uploaded Image'}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {formatDate(new Date(image.createdAt).getTime())}
                        </p>
                      </div>
                      <div className="text-gray-400 group-hover:text-violets-are-blue transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {isLoading && (
        <div className="text-center py-4">
          <div className="text-violets-are-blue text-sm">Adding image to canvas...</div>
        </div>
      )}

      <div className="text-xs text-gray-500 bg-neutral-800 p-3 rounded-lg mt-6">
        <p className="mb-1">💡 <strong>Tips:</strong></p>
        <p>• Click any image to add it to the canvas</p>
        <p>• Recent uploads are stored locally</p>
        <p>• Only the last 20 uploads are kept</p>
      </div>
    </div>
  );
};

export const CanvasSidebar = ({
  canvas, agentId, planId,
}: CanvasSidebarProps) => {
  const [activeTab, setActiveTab] = useState('generate');
  const [hasCanvasContent, setHasCanvasContent] = useState(false);

  useEffect(() => {
    const checkCanvasContent = () => {
      if (canvas) {
        const objects = canvas.getObjects();
        setHasCanvasContent(objects.length > 0);
      } else {
        setHasCanvasContent(false);
      }
    };

    checkCanvasContent();

    if (canvas) {
      canvas.on('object:added', checkCanvasContent);
      canvas.on('object:removed', checkCanvasContent);
      canvas.on('canvas:cleared', checkCanvasContent);

      return () => {
        canvas.off('object:added', checkCanvasContent);
        canvas.off('object:removed', checkCanvasContent);
        canvas.off('canvas:cleared', checkCanvasContent);
      };
    }
  }, [canvas]);

  const tabs: SidebarTab[] = [
    {
      id: "generate",
      icon: Wand2,
      label: "Generate",
      content: GenerateImagePanel,
    },
    {
      id: "improve",
      icon: Sparkles,
      label: "Improve",
      content: ImproveImagePanel,
    },
    {
      id: "upload",
      icon: Upload,
      label: "Upload",
      content: UploadImagePanel,
    },
    {
      id: "text",
      icon: Type,
      label: "Text",
      content: TextPanel,
    },
    {
      id: "recent",
      icon: Clock,
      label: "Recent",
      content: RecentUploadsPanel,
    },
  ];

  const ActiveContent = tabs.find(tab => tab.id === activeTab)?.content || GenerateImagePanel;

  return (
    <div className="w-[480px] bg-neutral-900 border-r border-neutral-700 flex h-full">
      <div className="w-20 bg-neutral-800 flex flex-col border-r border-neutral-700">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isImproveTab = tab.id === 'improve';
          const isDisabled = isImproveTab && !hasCanvasContent;

          return (
            <button
              key={tab.id}
              onClick={() => !isDisabled && setActiveTab(tab.id)}
              disabled={isDisabled}
              className={`p-4 flex flex-col items-center gap-1 transition-all duration-200 ${
                isDisabled
                  ? 'text-gray-600 cursor-not-allowed opacity-50'
                  : activeTab === tab.id
                    ? 'bg-gradient-to-tr from-violets-are-blue to-han-purple text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-neutral-700'
              }`}
              title={isDisabled ? 'Add content to canvas first' : tab.label}
            >
              <Icon size={18} />
              <span className="text-xs font-medium">{tab.label}</span>
            </button>
          );
        })}
      </div>

      <div className="flex-1 overflow-y-auto bg-neutral-900">
        <div className="h-full">
          <ActiveContent canvas={canvas} agentId={agentId} planId={planId} />
        </div>
      </div>
    </div>
  );
};
